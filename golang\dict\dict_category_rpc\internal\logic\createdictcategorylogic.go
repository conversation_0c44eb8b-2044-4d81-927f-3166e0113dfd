package logic

import (
	"context"
	"errors"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"
	"dict_category_rpc/model"
	"dict_rpc/dict"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CreateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典分类
func (l *CreateDictCategoryLogic) CreateDictCategory(in *dict_category.CreateDictCategoryReq) (*dict_category.CreateDictCategoryResp, error) {
	// 参数验证
	if in.DictId <= 0 {
		return &dict_category.CreateDictCategoryResp{
			Id:      0,
			Message: "字典ID不能为空",
		}, nil
	}

	if in.Name == "" {
		return &dict_category.CreateDictCategoryResp{
			Id:      0,
			Message: "字典分类名称不能为空",
		}, nil
	}

	// 验证字典是否存在
	_, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: in.DictId,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict_category.CreateDictCategoryResp{
				Id:      0,
				Message: "字典不存在",
			}, nil
		}
		return &dict_category.CreateDictCategoryResp{
			Id:      0,
			Message: "查询字典失败",
		}, nil
	}

	// 插入数据
	category := model.DictCategory{
		DictID: in.DictId,
		Name:   in.Name,
		Status: in.Status,
	}
	err = l.svcCtx.DictCategoryModel.Create(&category)
	if err != nil {
		return &dict_category.CreateDictCategoryResp{
			Id:      0,
			Message: "创建字典分类失败",
		}, nil
	}

	// 查询记录
	data, err := l.svcCtx.DictCategoryModel.FindByID(in.DictId)
	
	if err != nil {
		return &dict_category.CreateDictCategoryResp{
			Id:      0,
			Message: "查询字典分类失败",
		}, nil
	}

	return &dict_category.CreateDictCategoryResp{
		Id:      data.ID,
		Message: "创建字典分类成功",
	}, nil
}
