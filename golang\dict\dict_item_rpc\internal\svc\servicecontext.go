package svc

import (
	"dict_item_rpc/internal/config"
	"dict_item_rpc/model"

	"dict_rpc/dictservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config        config.Config
	DictRpc       dictservice.DictService
	
	DictItemModel *model.DictItemModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:        c,
		DictRpc:       dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
		DictItemModel: model.NewDictItemModel(model.NewDb(c.Mysql.DataSource)),
	}
}
