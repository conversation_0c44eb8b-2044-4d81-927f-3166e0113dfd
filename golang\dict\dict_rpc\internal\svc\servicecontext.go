package svc

import (
	"dict_category_rpc/dictcategoryservice"
	"dict_item_rpc/dictitemservice"
	"dict_rpc/internal/config"
	"dict_rpc/model"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config          config.Config
	DictModel       model.DictModel
	DictCategoryRpc dictcategoryservice.DictCategoryService
	DictItemRpc     dictitemservice.DictItemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:          c,
		DictModel:       *model.NewDictModel(model.NewDb(c.Mysql.DataSource)),
		DictCategoryRpc: dictcategoryservice.NewDictCategoryService(zrpc.MustNewClient(c.DictCategoryRpc)),
		DictItemRpc:     dictitemservice.NewDictItemService(zrpc.MustNewClient(c.DictItemRpc)),
	}
}
