package svc

import (
	"dict_category_rpc/internal/config"
	"dict_category_rpc/model"
	"dict_item_rpc/dictitemservice"
	"dict_rpc/dictservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config            config.Config
	DictCategoryModel model.DictCategoryModel
	DictRpc           dictservice.DictService
	DictItemRpc       dictitemservice.DictItemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:            c,
		DictCategoryModel: *model.NewDictCategoryModel(model.NewDb(c.Mysql.DataSource)),
		DictRpc:           dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
		DictItemRpc:       dictitemservice.NewDictItemService(zrpc.MustNewClient(c.DictItemRpc)),
	}
}
