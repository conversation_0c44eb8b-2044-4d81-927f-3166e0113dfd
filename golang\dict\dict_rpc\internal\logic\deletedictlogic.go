package logic

import (
	"context"
	"dict_category_rpc/dict_category"
	"dict_item_rpc/dict_item"
	"errors"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type DeleteDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典
func (l *DeleteDictLogic) DeleteDict(in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	// 参数验证
	if in.Id <= 0 {
		return &dict.DeleteDictResp{
			Success: false,
			Message: "字典ID不能为空",
		}, nil
	}

	// 检查字典是否存在
	_, err := l.svcCtx.DictModel.FindByID(in.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict.DeleteDictResp{
				Success: false,
				Message: "字典不存在",
			}, nil
		}
		return &dict.DeleteDictResp{
			Success: false,
			Message: "查询字典失败: " + err.Error(),
		}, nil
	}

	// 删除字典 - 按照关系先删除子记录

	// 1. 先删除所有相关的字典项 (dict_item表)
	// 获取所有属于该字典的字典项
	dictItemsResp, err := l.svcCtx.DictItemRpc.ListDictItem(l.ctx, &dict_item.ListDictItemReq{
		Page:     1,
		PageSize: 1000, 
		DictId:   in.Id,
		Status:   -1, // 获取所有状态的记录
	})
	if err != nil {
		return &dict.DeleteDictResp{
			Success: false,
			Message: "获取字典项列表失败: " + err.Error(),
		}, nil
	}

	// 逐个删除字典项
	if dictItemsResp.Total > 0 {
		for _, item := range dictItemsResp.List {
			deleteItemResp, err := l.svcCtx.DictItemRpc.DeleteDictItem(l.ctx, &dict_item.DeleteDictItemReq{
				Id: item.Id,
			})
			if err != nil {

				return &dict.DeleteDictResp{
					Success: false,
					Message: "删除字典项失败: " + err.Error(),
				}, nil
			}
			if !deleteItemResp.Success {

				return &dict.DeleteDictResp{
					Success: false,
					Message: "删除字典项失败: " + deleteItemResp.Message,
				}, nil
			}
		}

	}

	// 2. 再删除所有相关的字典分类 (dict_category表)
	// 获取所有属于该字典的分类
	dictCategoriesResp, err := l.svcCtx.DictCategoryRpc.ListDictCategory(l.ctx, &dict_category.ListDictCategoryReq{
		Page:     1,
		PageSize: 1000, // 设置一个较大的值来获取所有记录
		DictId:   in.Id,
		Status:   -1, // 获取所有状态的记录
	})
	if err != nil {

		return &dict.DeleteDictResp{
			Success: false,
			Message: "获取字典分类列表失败: " + err.Error(),
		}, nil
	}

	// 逐个删除字典分类
	if dictCategoriesResp.Total > 0 {
		for _, category := range dictCategoriesResp.List {
			deleteCategoryResp, err := l.svcCtx.DictCategoryRpc.DeleteDictCategory(l.ctx, &dict_category.DeleteDictCategoryReq{
				Id: category.Id,
			})
			if err != nil {

				return &dict.DeleteDictResp{
					Success: false,
					Message: "删除字典分类失败: " + err.Error(),
				}, nil
			}
			if !deleteCategoryResp.Success {

				return &dict.DeleteDictResp{
					Success: false,
					Message: "删除字典分类失败: " + deleteCategoryResp.Message,
				}, nil
			}
		}

	}

	// 3. 最后删除字典本身
	err = l.svcCtx.DictModel.Delete(in.Id)
	if err != nil {
		return &dict.DeleteDictResp{
			Success: false,
			Message: "删除字典失败: " + err.Error(),
		}, nil
	}

	return &dict.DeleteDictResp{
		Success: true,
		Message: "删除字典成功",
	}, nil
}
