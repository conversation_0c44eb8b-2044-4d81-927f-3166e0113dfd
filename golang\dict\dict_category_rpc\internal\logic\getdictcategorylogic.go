package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典分类详情
func (l *GetDictCategoryLogic) GetDictCategory(in *dict_category.GetDictCategoryReq) (*dict_category.GetDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	// 参数验证
	if in.Id <= 0 {
		return &dict_category.GetDictCategoryResp{
			Category: nil,
			Message:  "分类ID不能为空",
		}, nil
	}

	// 查询记录
	data, err := l.svcCtx.DictCategoryModel.FindByID(in.Id)
	if err != nil {
		return &dict_category.GetDictCategoryResp{
			Category: nil,
			Message:  "查询字典分类失败",
		}, nil
	}

	// 返回数据
	return &dict_category.GetDictCategoryResp{
		Category: &dict_category.DictCategory{
			Id:          data.ID,
			DictId:      data.DictID,
			Name:        data.Name,
			Status:      data.Status,
			CreatedTime: data.CreatedTime.Format("2006-01-02 15:04:05"),
			UpdatedTime: data.UpdatedTime.Format("2006-01-02 15:04:05"),
		},
		Message: "获取字典分类详情成功",
	}, nil
}
