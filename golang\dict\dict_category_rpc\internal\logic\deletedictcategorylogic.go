package logic

import (
	"context"
	"dict_item_rpc/dict_item"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	// 参数验证
	if in.Id <= 0 {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "分类ID不能为空",
		}, nil
	}

	// 检查字典分类是否存在
	_, err := l.svcCtx.DictCategoryModel.FindByID(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "字典分类不存在",
		}, nil
	}

	// 1. 先删除该分类下的所有字典项
	// 获取该分类下的所有字典项
	dictItemsResp, err := l.svcCtx.DictItemRpc.ListDictItem(l.ctx, &dict_item.ListDictItemReq{
		Page:       1,
		PageSize:   1000, // 设置一个较大的值来获取所有记录
		CategoryId: in.Id,
		Status:     -1, // 获取所有状态的记录
	})
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "获取字典项列表失败: " + err.Error(),
		}, nil
	}

	// 逐个删除字典项
	if dictItemsResp.Total > 0 {
		for _, item := range dictItemsResp.List {
			deleteItemResp, err := l.svcCtx.DictItemRpc.DeleteDictItem(l.ctx, &dict_item.DeleteDictItemReq{
				Id: item.Id,
			})
			if err != nil {
				return &dict_category.DeleteDictCategoryResp{
					Success: false,
					Message: "删除字典项失败: " + err.Error(),
				}, nil
			}
			if !deleteItemResp.Success {
				return &dict_category.DeleteDictCategoryResp{
					Success: false,
					Message: "删除字典项失败: " + deleteItemResp.Message,
				}, nil
			}
		}
		logx.Infof("成功删除分类下的 %d 个字典项", len(dictItemsResp.List))
	}

	// 2. 删除字典分类本身
	err = l.svcCtx.DictCategoryModel.Delete(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "删除字典分类失败: " + err.Error(),
		}, nil
	}

	return &dict_category.DeleteDictCategoryResp{
		Success: true,
		Message: "删除字典分类成功",
	}, nil
}
